// Views/MainWindow.xaml.cs
using System.Windows;
using DNP3Editor.ViewModels;

namespace DNP3Editor.Views;

public partial class MainWindow : Window
{
    public MainWindow(MainWindowViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
    }
}

// Controls/NavigationTreeControl.xaml.cs
using System.Windows;
using System.Windows.Controls;
using DNP3Editor.ViewModels;
using DNP3Editor.Models;

namespace DNP3Editor.Controls;

public partial class NavigationTreeControl : UserControl
{
    public NavigationTreeControl()
    {
        InitializeComponent();
    }

    private void TreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        if (DataContext is TreeNavigationViewModel viewModel && e.NewValue is NavigationNode node)
        {
            viewModel.SelectNodeCommand.Execute(node);
        }
    }
}

// Controls/PropertiesPanelControl.xaml.cs
using System.Windows.Controls;

namespace DNP3Editor.Controls;

public partial class PropertiesPanelControl : UserControl
{
    public PropertiesPanelControl()
    {
        InitializeComponent();
    }
}

// Controls/OutputWindowControl.xaml.cs
using System.Windows.Controls;

namespace DNP3Editor.Controls;

public partial class OutputWindowControl : UserControl
{
    public OutputWindowControl()
    {
        InitializeComponent();
    }
}

// Views/DeviceConfigurationView.xaml.cs
using System.Windows.Controls;

namespace DNP3Editor.Views;

public partial class DeviceConfigurationView : UserControl
{
    public DeviceConfigurationView()
    {
        InitializeComponent();
    }
}

// Views/DataPointsView.xaml.cs
using System.Windows.Controls;

namespace DNP3Editor.Views;

public partial class DataPointsView : UserControl
{
    public DataPointsView()
    {
        InitializeComponent();
    }
}

// Themes/DNP3EditorStyles.xaml
/*
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- Custom styles for the DNP3 Editor -->
    <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Margin" Value="0,10,0,5"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundAccentBrush}"/>
    </Style>
    
    <Style x:Key="FieldLabelStyle" TargetType="Label">
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="MinWidth" Value="120"/>
    </Style>
    
    <Style x:Key="ValidationErrorStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="Red"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>
    
</ResourceDictionary>
*/

// Scripts/GenerateClasses.ps1
/*
# PowerShell script to generate C# classes from XSD
param(
    [Parameter(Mandatory=$true)]
    [string]$XsdPath,
    
    [Parameter(Mandatory=$true)]
    [string]$OutputPath,
    
    [string]$Namespace = "DNP3Editor.Generated"
)

Write-Host "Generating C# classes from XSD..."
Write-Host "XSD Path: $XsdPath"
Write-Host "Output Path: $OutputPath"
Write-Host "Namespace: $Namespace"

# Check if XmlSchemaClassGenerator is installed
$generator = Get-Command "XmlSchemaClassGenerator" -ErrorAction SilentlyContinue
if (-not $generator) {
    Write-Host "Installing XmlSchemaClassGenerator..."
    dotnet tool install --global XmlSchemaClassGenerator.Console
}

# Create output directory if it doesn't exist
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force
}

# Generate classes
$command = @(
    "--namespace", $Namespace,
    "--output", $OutputPath,
    "--nullable-references",
    "--net-core",
    "--data-annotations",
    "--separate-classes",
    "--pascal-case",
    "--verbose",
    $XsdPath
)

Write-Host "Running: XmlSchemaClassGenerator $($command -join ' ')"
& XmlSchemaClassGenerator @command

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Classes generated successfully!" -ForegroundColor Green
    
    # Post-process generated files to add custom attributes and partial classes
    Get-ChildItem -Path $OutputPath -Filter "*.cs" | ForEach-Object {
        $content = Get-Content $_.FullName -Raw
        
        # Add custom using statements
        $customUsings = @"
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using CommunityToolkit.Mvvm.ComponentModel;

"@
        
        # Insert after existing usings
        $content = $content -replace "(using System\.Xml\.Serialization;)", "`$1`n$customUsings"
        
        # Make classes partial for extensibility
        $content = $content -replace "public class ", "public partial class "
        
        Set-Content $_.FullName -Value $content
    }
    
    Write-Host "✅ Post-processing completed!" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to generate classes!" -ForegroundColor Red
    exit 1
}
*/

// Scripts/BuildProject.ps1
/*
# PowerShell script to build the entire project
param(
    [string]$Configuration = "Debug",
    [switch]$Clean,
    [switch]$GenerateClasses
)

$projectRoot = Split-Path $PSScriptRoot -Parent
$xsdPath = Join-Path $projectRoot "DNP3DeviceProfileNovember2014.xsd"
$generatedPath = Join-Path $projectRoot "Generated"

Write-Host "🏗️  Building DNP3 Device Profile Editor" -ForegroundColor Cyan
Write-Host "Configuration: $Configuration"

if ($Clean) {
    Write-Host "🧹 Cleaning solution..."
    dotnet clean --configuration $Configuration
    if (Test-Path $generatedPath) {
        Remove-Item $generatedPath -Recurse -Force
    }
}

if ($GenerateClasses -or -not (Test-Path $generatedPath)) {
    Write-Host "🔄 Generating C# classes from XSD..."
    & "$PSScriptRoot/GenerateClasses.ps1" -XsdPath $xsdPath -OutputPath $generatedPath -Namespace "DNP3Editor.Generated"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to generate classes!" -ForegroundColor Red
        exit 1
    }
}

Write-Host "🔨 Building solution..."
dotnet build --configuration $Configuration --no-restore

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Build completed successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    exit 1
}
*/

// Generated/EmptyElement.cs (example of what will be generated)
namespace DNP3Editor.Generated;

/// <summary>
/// Represents an empty XML element used in DNP3 schema
/// </summary>
[System.CodeDom.Compiler.GeneratedCode("XmlSchemaClassGenerator", "*******")]
[System.Xml.Serialization.XmlType("emptyElement", Namespace = "http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
public partial class EmptyElement
{
    // Empty element - no properties
}

// Generated/EventClassType.cs (example enum)
namespace DNP3Editor.Generated;

/// <summary>
/// DNP3 Event Class enumeration
/// </summary>
[System.CodeDom.Compiler.GeneratedCode("XmlSchemaClassGenerator", "*******")]
[System.Xml.Serialization.XmlType("eventClassType", Namespace = "http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
public enum EventClassType
{
    [System.Xml.Serialization.XmlEnum("none")]
    None,
    
    [System.Xml.Serialization.XmlEnum("one")]
    One,
    
    [System.Xml.Serialization.XmlEnum("two")]
    Two,
    
    [System.Xml.Serialization.XmlEnum("three")]
    Three,
}

// Extensions/DNP3Extensions.cs - Custom extensions for generated classes


namespace DNP3Editor.Extensions;

public static class DNP3Extensions
{
    /// <summary>
    /// Gets a user-friendly display name for the event class
    /// </summary>
    public static string GetDisplayName(this EventClassType eventClass)
    {
        return eventClass switch
        {
            EventClassType.None => "No Events",
            EventClassType.One => "Class 1 (High Priority)",
            EventClassType.Two => "Class 2 (Medium Priority)", 
            EventClassType.Three => "Class 3 (Low Priority)",
            _ => eventClass.ToString()
        };
    }

    /// <summary>
    /// Validates if a data link address is in the valid DNP3 range
    /// </summary>
    public static bool IsValidDataLinkAddress(this ushort address)
    {
        return address < 65520; // 65520-65535 are reserved
    }

    /// <summary>
    /// Gets the DNP3 object group for a specific point type
    /// </summary>
    public static int GetObjectGroup<T>(this T point) where T : class
    {
        return point switch
        {
            BinaryInputPointType => 1,
            DoubleBitInputPointType => 3,
            BinaryOutputPointType => 10,
            CounterPointType => 20,
            AnalogInputPointType => 30,
            AnalogOutputPointType => 40,
            _ => 0
        };
    }

    /// <summary>
    /// Creates a default device configuration
    /// </summary>
    public static DeviceConfigType CreateDefaultDeviceConfig(string vendorName, string deviceName)
    {
        return new DeviceConfigType
        {
            VendorName = new VendorNameType
            {
                CurrentValue = new VendorNameCurrentValueType { Value = vendorName }
            },
            DeviceName = new DeviceNameType
            {
                CurrentValue = new DeviceNameCurrentValueType { Value = deviceName }
            },
            DeviceFunction = new DeviceFunctionType
            {
                CurrentValue = new DeviceFunctionCurrentValueType
                {
                    Outstation = new EmptyElement() // Default to outstation
                }
            }
        };
    }
}

// Utilities/XsdValidator.cs - XSD validation utility
using System.Xml;
using System.Xml.Schema;
using System.Xml.Serialization;

using DNP3Editor.Models;

namespace DNP3Editor.Utilities;

public static class XsdValidator
{
    private static XmlSchemaSet? _schemaSet;

    public static ValidationResult ValidateDocument(DNP3DeviceProfileDocument document)
    {
        var result = new ValidationResult();

        try
        {
            // Serialize document to XML
            var serializer = new XmlSerializer(typeof(DNP3DeviceProfileDocument));
            using var memoryStream = new MemoryStream();
            serializer.Serialize(memoryStream, document);

            // Validate against XSD
            memoryStream.Position = 0;
            var settings = new XmlReaderSettings
            {
                ValidationType = ValidationType.Schema,
                Schemas = GetSchemaSet()
            };

            settings.ValidationEventHandler += (sender, e) =>
            {
                if (e.Severity == XmlSeverityType.Error)
                {
                    result.Errors.Add($"Line {e.Exception?.LineNumber}: {e.Message}");
                }
                else
                {
                    result.Warnings.Add($"Line {e.Exception?.LineNumber}: {e.Message}");
                }
            };

            using var reader = XmlReader.Create(memoryStream, settings);
            while (reader.Read()) { } // Process entire document

            result.IsValid = result.Errors.Count == 0;
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Validation error: {ex.Message}");
            result.IsValid = false;
        }

        return result;
    }

    private static XmlSchemaSet GetSchemaSet()
    {
        if (_schemaSet == null)
        {
            _schemaSet = new XmlSchemaSet();
            var schemaPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DNP3DeviceProfileNovember2014.xsd");
            
            if (File.Exists(schemaPath))
            {
                _schemaSet.Add("http://www.dnp3.org/DNP3/DeviceProfile/November2014", schemaPath);
                _schemaSet.Compile();
            }
        }

        return _schemaSet;
    }
}

// Setup/ProjectSetup.md (Documentation)
/*
# DNP3 Device Profile Editor - Project Setup

## Prerequisites
- .NET 8.0 SDK
- Visual Studio 2022 or VS Code
- XmlSchemaClassGenerator tool

## Initial Setup

1. **Clone/Create Project**
   ```bash
   mkdir DNP3Editor
   cd DNP3Editor
   dotnet new wpf
   ```

2. **Install Required Packages**
   ```bash
   dotnet add package CommunityToolkit.Mvvm
   dotnet add package Microsoft.Extensions.DependencyInjection
   dotnet add package Microsoft.Extensions.Hosting
   dotnet add package Serilog
   dotnet add package ModernWpfUI
   dotnet add package FluentValidation
   ```

3. **Install XSD Generator Tool**
   ```bash
   dotnet tool install --global XmlSchemaClassGenerator.Console
   ```

4. **Generate Classes from XSD**
   ```bash
   powershell ./Scripts/GenerateClasses.ps1 -XsdPath ./DNP3DeviceProfileNovember2014.xsd -OutputPath ./Generated
   ```

5. **Build Project**
   ```bash
   dotnet build
   ```

## Development Workflow

1. **After XSD Changes**: Run `GenerateClasses.ps1` to regenerate classes
2. **Before Committing**: Run `BuildProject.ps1 -Clean -GenerateClasses` to ensure clean build
3. **Custom Extensions**: Add to `Extensions/` folder using partial classes

## Project Structure
```
DNP3Editor/
├── Generated/           # Auto-generated from XSD (do not edit)
├── ViewModels/          # MVVM ViewModels
├── Views/               # WPF Views
├── Controls/            # Custom UserControls
├── Services/            # Business services
├── Models/              # Data models
├── Extensions/          # Extensions for generated classes
├── Utilities/           # Helper utilities
├── Themes/              # XAML styles and themes
└── Scripts/             # Build and generation scripts
```

## Key Features Implemented

✅ **Core Architecture**
- MVVM pattern with CommunityToolkit.Mvvm
- Dependency injection with Microsoft.Extensions.DI
- Modern WPF UI with ModernWpfUI

✅ **Document Management** 
- XML loading/saving with XSD validation
- Recent files tracking
- Undo/redo support (TODO)

✅ **Navigation & Editing**
- Tree-based navigation of document structure
- Properties panel for selected items
- Tabbed editing interface

✅ **Data Points Management**
- Grid-based editing of different point types
- Add/delete/duplicate operations
- Import/export capabilities (TODO)

✅ **Validation**
- Real-time XSD validation
- Business rule validation
- Output window for errors/warnings

## Next Steps

1. **Complete Generated Classes**: Verify all XSD types are properly generated
2. **Enhanced UI**: Add icons, better styling, data templates
3. **Advanced Features**: Import/export, templates, wizards
4. **Testing**: Unit tests for services and validation
5. **Packaging**: MSI installer with licensing
*/