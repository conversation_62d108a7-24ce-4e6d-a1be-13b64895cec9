<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyTitle>DNP3 Device Profile Editor</AssemblyTitle>
    <AssemblyDescription>Professional DNP3 Device Configuration Editor</AssemblyDescription>
    <AssemblyCompany>Your Company Name</AssemblyCompany>
    <AssemblyProduct>DNP3 Editor Pro</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <ApplicationIcon>Resources\app.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="code-behind-and-generation.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="FluentValidation" Version="11.8.1" />
    <PackageReference Include="ModernWpfUI" Version="0.9.6" />
    <PackageReference Include="System.ComponentModel.DataAnnotations" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Generated\" />
    <Folder Include="Resources\" />
    <Folder Include="Themes\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="code-behind-and-generation.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Update="DNP3DeviceProfileNovember2014.xsd">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>