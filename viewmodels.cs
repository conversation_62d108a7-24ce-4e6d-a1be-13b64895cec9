// ViewModels/TreeNavigationViewModel.cs
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;

using DNP3Editor.Models;
using DNP3Editor.Events;

namespace DNP3Editor.ViewModels;

public partial class TreeNavigationViewModel : ObservableObject
{
    [ObservableProperty]
    private NavigationNode? selectedNode;

    public ObservableCollection<NavigationNode> RootNodes { get; } = new();

    public event EventHandler<NavigationNodeSelectedEventArgs>? NodeSelected;

    [RelayCommand]
    private void SelectNode(NavigationNode node)
    {
        if (SelectedNode != null)
        {
            SelectedNode.IsSelected = false;
        }

        SelectedNode = node;
        node.IsSelected = true;
        
        NodeSelected?.Invoke(this, new NavigationNodeSelectedEventArgs(node));
    }

    [RelayCommand]
    private void ExpandNode(NavigationNode node)
    {
        node.IsExpanded = !node.IsExpanded;
    }

    public void LoadDocument(DNP3DeviceProfileDocument document)
    {
        RootNodes.Clear();

        var documentNode = new NavigationNode
        {
            Id = "document",
            Name = "DNP3 Device Profile",
            Description = document.DocumentHeader?.DocumentName ?? "Unnamed Document",
            NodeType = NavigationNodeType.Document,
            Data = document,
            IsExpanded = true
        };

        RootNodes.Add(documentNode);

        if (document.ReferenceDevice != null)
        {
            BuildDeviceNodes(documentNode, document.ReferenceDevice);
        }
    }

    private void BuildDeviceNodes(NavigationNode parent, DNP3DeviceOptionalType device)
    {
        if (device.Configuration != null)
        {
            var configNode = CreateNode("configuration", "Configuration", "Device Configuration", 
                NavigationNodeType.Configuration, device.Configuration, parent);

            BuildConfigurationNodes(configNode, device.Configuration);
        }

        if (device.DataPointsList != null)
        {
            var dataPointsNode = CreateNode("datapoints", "Data Points", "Device Data Points", 
                NavigationNodeType.DataPoints, device.DataPointsList, parent);

            BuildDataPointNodes(dataPointsNode, device.DataPointsList);
        }
    }

    private void BuildConfigurationNodes(NavigationNode parent, DNP3ConfigurationType configuration)
    {
        if (configuration.DeviceConfig != null)
        {
            CreateNode("deviceinfo", "Device Information", "Basic device information", 
                NavigationNodeType.DeviceInfo, configuration.DeviceConfig, parent);
        }

        if (configuration.SerialConfig != null)
        {
            CreateNode("serial", "Serial Configuration", "Serial communication settings", 
                NavigationNodeType.Serial, configuration.SerialConfig, parent);
        }

        if (configuration.NetworkConfig != null)
        {
            CreateNode("network", "Network Configuration", "Network communication settings", 
                NavigationNodeType.Network, configuration.NetworkConfig, parent);
        }

        if (configuration.SecurityConfig != null)
        {
            CreateNode("security", "Security Configuration", "Security and authentication settings", 
                NavigationNodeType.Security, configuration.SecurityConfig, parent);
        }

        if (configuration.MasterConfig != null)
        {
            CreateNode("master", "Master Configuration", "DNP3 master specific settings", 
                NavigationNodeType.Master, configuration.MasterConfig, parent);
        }

        if (configuration.OutstationConfig != null)
        {
            CreateNode("outstation", "Outstation Configuration", "DNP3 outstation specific settings", 
                NavigationNodeType.Outstation, configuration.OutstationConfig, parent);
        }
    }

    private void BuildDataPointNodes(NavigationNode parent, DNP3DataPointsListType dataPoints)
    {
        if (dataPoints.BinaryInputPoints != null)
        {
            var node = CreateNode("binaryinputs", "Binary Inputs", 
                $"Binary input points ({dataPoints.BinaryInputPoints.BinaryInput?.Length ?? 0})", 
                NavigationNodeType.BinaryInput, dataPoints.BinaryInputPoints, parent);
        }

        if (dataPoints.DoubleBitInputPoints != null)
        {
            CreateNode("doublebitinputs", "Double-bit Inputs", 
                $"Double-bit input points ({dataPoints.DoubleBitInputPoints.DoubleBitInput?.Length ?? 0})", 
                NavigationNodeType.DoubleBitInput, dataPoints.DoubleBitInputPoints, parent);
        }

        if (dataPoints.BinaryOutputPoints != null)
        {
            CreateNode("binaryoutputs", "Binary Outputs", 
                $"Binary output points ({dataPoints.BinaryOutputPoints.BinaryOutput?.Length ?? 0})", 
                NavigationNodeType.BinaryOutput, dataPoints.BinaryOutputPoints, parent);
        }

        if (dataPoints.AnalogInputPoints != null)
        {
            CreateNode("analoginputs", "Analog Inputs", 
                $"Analog input points ({dataPoints.AnalogInputPoints.AnalogInput?.Length ?? 0})", 
                NavigationNodeType.AnalogInput, dataPoints.AnalogInputPoints, parent);
        }

        if (dataPoints.AnalogOutputPoints != null)
        {
            CreateNode("analogoutputs", "Analog Outputs", 
                $"Analog output points ({dataPoints.AnalogOutputPoints.AnalogOutput?.Length ?? 0})", 
                NavigationNodeType.AnalogOutput, dataPoints.AnalogOutputPoints, parent);
        }

        if (dataPoints.CounterPoints != null)
        {
            CreateNode("counters", "Counters", 
                $"Counter points ({dataPoints.CounterPoints.Counter?.Length ?? 0})", 
                NavigationNodeType.Counter, dataPoints.CounterPoints, parent);
        }
    }

    private NavigationNode CreateNode(string id, string name, string description, 
        NavigationNodeType nodeType, object data, NavigationNode parent)
    {
        var node = new NavigationNode
        {
            Id = id,
            Name = name,
            Description = description,
            NodeType = nodeType,
            Data = data,
            Parent = parent
        };

        parent.Children.Add(node);
        return node;
    }
}

// ViewModels/PropertiesPanelViewModel.cs
using CommunityToolkit.Mvvm.ComponentModel;
using System.Collections.ObjectModel;
using DNP3Editor.Models;

namespace DNP3Editor.ViewModels;

public partial class PropertiesPanelViewModel : ObservableObject
{
    [ObservableProperty]
    private string selectedObjectName = "No Selection";

    [ObservableProperty]
    private string selectedObjectType = "";

    public ObservableCollection<PropertyItem> Properties { get; } = new();

    public void LoadProperties(NavigationNode node)
    {
        SelectedObjectName = node.Name;
        SelectedObjectType = node.NodeType.ToString();

        Properties.Clear();

        if (node.Data != null)
        {
            LoadPropertiesFromObject(node.Data);
        }
    }

    private void LoadPropertiesFromObject(object obj)
    {
        var type = obj.GetType();
        var properties = type.GetProperties();

        foreach (var prop in properties)
        {
            if (prop.CanRead && IsDisplayableProperty(prop))
            {
                var value = prop.GetValue(obj);
                Properties.Add(new PropertyItem
                {
                    Name = prop.Name,
                    Value = value?.ToString() ?? "",
                    Type = prop.PropertyType.Name,
                    IsReadOnly = !prop.CanWrite
                });
            }
        }
    }

    private static bool IsDisplayableProperty(System.Reflection.PropertyInfo prop)
    {
        // Filter out complex navigation properties and internal properties
        var type = prop.PropertyType;
        
        return type.IsPrimitive || 
               type == typeof(string) || 
               type == typeof(DateTime) ||
               type == typeof(bool) ||
               type.IsEnum ||
               (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>));
    }
}

public class PropertyItem
{
    public string Name { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public bool IsReadOnly { get; set; }
}

// ViewModels/OutputWindowViewModel.cs
using CommunityToolkit.Mvvm.ComponentModel;
using System.Collections.ObjectModel;
using DNP3Editor.Models;

namespace DNP3Editor.ViewModels;

public partial class OutputWindowViewModel : ObservableObject
{
    public ObservableCollection<OutputMessage> Messages { get; } = new();

    [ObservableProperty]
    private bool showInfo = true;

    [ObservableProperty]
    private bool showWarnings = true;

    [ObservableProperty]
    private bool showErrors = true;

    public void AddMessage(string message, MessageType type)
    {
        var outputMessage = new OutputMessage
        {
            Message = message,
            Type = type,
            Timestamp = DateTime.Now
        };

        // Add to UI thread
        App.Current.Dispatcher.Invoke(() =>
        {
            Messages.Add(outputMessage);
            
            // Limit messages to prevent memory issues
            if (Messages.Count > 1000)
            {
                Messages.RemoveAt(0);
            }
        });
    }

    public void Clear()
    {
        Messages.Clear();
    }
}

// ViewModels/DeviceConfigurationViewModel.cs
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;


namespace DNP3Editor.ViewModels;

public partial class DeviceConfigurationViewModel : ObservableObject
{
    private readonly DNP3ConfigurationType _configuration;

    [ObservableProperty]
    private string vendorName = string.Empty;

    [ObservableProperty]
    private string deviceName = string.Empty;

    [ObservableProperty]
    private string hardwareVersion = string.Empty;

    [ObservableProperty]
    private string softwareVersion = string.Empty;

    [ObservableProperty]
    private bool isMasterDevice;

    [ObservableProperty]
    private bool isOutstationDevice;

    public DeviceConfigurationViewModel(DNP3ConfigurationType configuration)
    {
        _configuration = configuration;
        LoadFromConfiguration();
    }

    [RelayCommand]
    private void SaveConfiguration()
    {
        SaveToConfiguration();
        // TODO: Mark document as modified
    }

    [RelayCommand]
    private void ResetConfiguration()
    {
        LoadFromConfiguration();
    }

    private void LoadFromConfiguration()
    {
        if (_configuration.DeviceConfig != null)
        {
            VendorName = _configuration.DeviceConfig.VendorName?.CurrentValue?.Value ?? "";
            DeviceName = _configuration.DeviceConfig.DeviceName?.CurrentValue?.Value ?? "";
            HardwareVersion = _configuration.DeviceConfig.HardwareVersion?.CurrentValue?.Value ?? "";
            SoftwareVersion = _configuration.DeviceConfig.SoftwareVersion?.CurrentValue?.Value ?? "";

            // Device function
            var deviceFunction = _configuration.DeviceConfig.DeviceFunction?.CurrentValue;
            IsMasterDevice = deviceFunction?.Master != null;
            IsOutstationDevice = deviceFunction?.Outstation != null;
        }
    }

    private void SaveToConfiguration()
    {
        if (_configuration.DeviceConfig == null)
        {
            _configuration.DeviceConfig = new DeviceConfigType();
        }

        // Update vendor name
        if (_configuration.DeviceConfig.VendorName == null)
        {
            _configuration.DeviceConfig.VendorName = new VendorNameType();
        }
        if (_configuration.DeviceConfig.VendorName.CurrentValue == null)
        {
            _configuration.DeviceConfig.VendorName.CurrentValue = new VendorNameCurrentValueType();
        }
        _configuration.DeviceConfig.VendorName.CurrentValue.Value = VendorName;

        // Update device name
        if (_configuration.DeviceConfig.DeviceName == null)
        {
            _configuration.DeviceConfig.DeviceName = new DeviceNameType();
        }
        if (_configuration.DeviceConfig.DeviceName.CurrentValue == null)
        {
            _configuration.DeviceConfig.DeviceName.CurrentValue = new DeviceNameCurrentValueType();
        }
        _configuration.DeviceConfig.DeviceName.CurrentValue.Value = DeviceName;

        // Update hardware version
        if (!string.IsNullOrEmpty(HardwareVersion))
        {
            if (_configuration.DeviceConfig.HardwareVersion == null)
            {
                _configuration.DeviceConfig.HardwareVersion = new HardwareVersionType();
            }
            if (_configuration.DeviceConfig.HardwareVersion.CurrentValue == null)
            {
                _configuration.DeviceConfig.HardwareVersion.CurrentValue = new HardwareVersionCurrentValueType();
            }
            _configuration.DeviceConfig.HardwareVersion.CurrentValue.Value = HardwareVersion;
        }

        // Update software version
        if (!string.IsNullOrEmpty(SoftwareVersion))
        {
            if (_configuration.DeviceConfig.SoftwareVersion == null)
            {
                _configuration.DeviceConfig.SoftwareVersion = new SoftwareVersionType();
            }
            if (_configuration.DeviceConfig.SoftwareVersion.CurrentValue == null)
            {
                _configuration.DeviceConfig.SoftwareVersion.CurrentValue = new SoftwareVersionCurrentValueType();
            }
            _configuration.DeviceConfig.SoftwareVersion.CurrentValue.Value = SoftwareVersion;
        }

        // Update device function
        if (_configuration.DeviceConfig.DeviceFunction == null)
        {
            _configuration.DeviceConfig.DeviceFunction = new DeviceFunctionType();
        }
        if (_configuration.DeviceConfig.DeviceFunction.CurrentValue == null)
        {
            _configuration.DeviceConfig.DeviceFunction.CurrentValue = new DeviceFunctionCurrentValueType();
        }

        if (IsMasterDevice)
        {
            _configuration.DeviceConfig.DeviceFunction.CurrentValue.Master = new EmptyElement();
            _configuration.DeviceConfig.DeviceFunction.CurrentValue.Outstation = null;
        }
        else if (IsOutstationDevice)
        {
            _configuration.DeviceConfig.DeviceFunction.CurrentValue.Outstation = new EmptyElement();
            _configuration.DeviceConfig.DeviceFunction.CurrentValue.Master = null;
        }
    }
}

// ViewModels/DataPointsViewModel.cs
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;

using DNP3Editor.Models;

namespace DNP3Editor.ViewModels;

public partial class DataPointsViewModel : ObservableObject
{
    private readonly DNP3DataPointsListType _dataPointsList;

    [ObservableProperty]
    private DataPointType selectedDataPointType = DataPointType.BinaryInput;

    [ObservableProperty]
    private object? selectedDataPoint;

    public ObservableCollection<BinaryInputPointType> BinaryInputPoints { get; } = new();
    public ObservableCollection<AnalogInputPointType> AnalogInputPoints { get; } = new();
    public ObservableCollection<BinaryOutputPointType> BinaryOutputPoints { get; } = new();
    public ObservableCollection<AnalogOutputPointType> AnalogOutputPoints { get; } = new();
    public ObservableCollection<CounterPointType> CounterPoints { get; } = new();

    public DataPointsViewModel(DNP3DataPointsListType dataPointsList)
    {
        _dataPointsList = dataPointsList;
        LoadDataPoints();
    }

    [RelayCommand]
    private void AddDataPoint()
    {
        switch (SelectedDataPointType)
        {
            case DataPointType.BinaryInput:
                AddBinaryInputPoint();
                break;
            case DataPointType.AnalogInput:
                AddAnalogInputPoint();
                break;
            case DataPointType.BinaryOutput:
                AddBinaryOutputPoint();
                break;
            case DataPointType.AnalogOutput:
                AddAnalogOutputPoint();
                break;
            case DataPointType.Counter:
                AddCounterPoint();
                break;
        }
    }

    [RelayCommand]
    private void DeleteDataPoint()
    {
        if (SelectedDataPoint == null) return;

        switch (SelectedDataPoint)
        {
            case BinaryInputPointType binaryInput:
                BinaryInputPoints.Remove(binaryInput);
                break;
            case AnalogInputPointType analogInput:
                AnalogInputPoints.Remove(analogInput);
                break;
            // Add other types...
        }

        SaveDataPoints();
    }

    [RelayCommand]
    private void DuplicateDataPoint()
    {
        if (SelectedDataPoint == null) return;

        // TODO: Implement duplication logic
    }

    private void LoadDataPoints()
    {
        // Load binary input points
        if (_dataPointsList.BinaryInputPoints?.BinaryInput != null)
        {
            BinaryInputPoints.Clear();
            foreach (var point in _dataPointsList.BinaryInputPoints.BinaryInput)
            {
                BinaryInputPoints.Add(point);
            }
        }

        // Load analog input points
        if (_dataPointsList.AnalogInputPoints?.AnalogInput != null)
        {
            AnalogInputPoints.Clear();
            foreach (var point in _dataPointsList.AnalogInputPoints.AnalogInput)
            {
                AnalogInputPoints.Add(point);
            }
        }

        // Load other point types...
    }

    private void SaveDataPoints()
    {
        // Update binary input points
        if (_dataPointsList.BinaryInputPoints == null)
        {
            _dataPointsList.BinaryInputPoints = new BinaryInputPointsType();
        }
        _dataPointsList.BinaryInputPoints.BinaryInput = BinaryInputPoints.ToArray();

        // Update analog input points
        if (_dataPointsList.AnalogInputPoints == null)
        {
            _dataPointsList.AnalogInputPoints = new AnalogInputPointsType();
        }
        _dataPointsList.AnalogInputPoints.AnalogInput = AnalogInputPoints.ToArray();

        // Update other point types...
    }

    private void AddBinaryInputPoint()
    {
        var newIndex = BinaryInputPoints.Count > 0 ? BinaryInputPoints.Max(p => p.Index) + 1 : 0;
        
        var newPoint = new BinaryInputPointType
        {
            Index = (uint)newIndex,
            Name = $"BinaryInput_{newIndex}",
            Description = "New binary input point",
            ChangeEventClass = EventClassType.One,
            IncludedInClass0Response = IncludedInClass0ResponseType.Always
        };

        BinaryInputPoints.Add(newPoint);
        SaveDataPoints();
    }

    private void AddAnalogInputPoint()
    {
        var newIndex = AnalogInputPoints.Count > 0 ? AnalogInputPoints.Max(p => p.Index) + 1 : 0;
        
        var newPoint = new AnalogInputPointType
        {
            Index = (uint)newIndex,
            Name = $"AnalogInput_{newIndex}",
            Description = "New analog input point",
            ChangeEventClass = EventClassType.One,
            IncludedInClass0Response = IncludedInClass0ResponseType.Always,
            Units = "Units",
            ScaleFactor = 1.0,
            ScaleOffset = 0.0
        };

        AnalogInputPoints.Add(newPoint);
        SaveDataPoints();
    }

    private void AddBinaryOutputPoint()
    {
        var newIndex = BinaryOutputPoints.Count > 0 ? BinaryOutputPoints.Max(p => p.Index) + 1 : 0;
        
        var newPoint = new BinaryOutputPointType
        {
            Index = (uint)newIndex,
            Name = $"BinaryOutput_{newIndex}",
            Description = "New binary output point",
            ChangeEventClass = EventClassType.One,
            CommandEventClass = EventClassType.One,
            IncludedInClass0Response = IncludedInClass0ResponseType.Always
        };

        BinaryOutputPoints.Add(newPoint);
        SaveDataPoints();
    }

    private void AddAnalogOutputPoint()
    {
        var newIndex = AnalogOutputPoints.Count > 0 ? AnalogOutputPoints.Max(p => p.Index) + 1 : 0;
        
        var newPoint = new AnalogOutputPointType
        {
            Index = (uint)newIndex,
            Name = $"AnalogOutput_{newIndex}",
            Description = "New analog output point",
            ChangeEventClass = EventClassType.One,
            CommandEventClass = EventClassType.One,
            IncludedInClass0Response = IncludedInClass0ResponseType.Always,
            Units = "Units",
            ScaleFactor = 1.0,
            ScaleOffset = 0.0
        };

        AnalogOutputPoints.Add(newPoint);
        SaveDataPoints();
    }

    private void AddCounterPoint()
    {
        var newIndex = CounterPoints.Count > 0 ? CounterPoints.Max(p => p.Index) + 1 : 0;
        
        var newPoint = new CounterPointType
        {
            Index = (uint)newIndex,
            Name = $"Counter_{newIndex}",
            Description = "New counter point",
            CounterEventClass = EventClassType.One,
            CountersIncludedInClass0 = IncludedInClass0ResponseType.Always,
            CounterRollOver = 65536
        };

        CounterPoints.Add(newPoint);
        SaveDataPoints();
    }
}

public enum DataPointType
{
    BinaryInput,
    DoubleBitInput,
    BinaryOutput,
    AnalogInput,
    AnalogOutput,
    Counter,
    OctetString,
    VirtualTerminal
}