<!-- Controls/NavigationTreeControl.xaml -->
<UserControl x:Class="DNP3Editor.Controls.NavigationTreeControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <Grid>
        <TreeView ItemsSource="{Binding RootNodes}" 
                  SelectedItemChanged="TreeView_SelectedItemChanged">
            <TreeView.ItemContainerStyle>
                <Style TargetType="TreeViewItem" BasedOn="{StaticResource {x:Type TreeViewItem}}">
                    <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}"/>
                    <Setter Property="IsSelected" Value="{Binding IsSelected, Mode=TwoWay}"/>
                </Style>
            </TreeView.ItemContainerStyle>
            <TreeView.ItemTemplate>
                <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                    <StackPanel Orientation="Horizontal" Margin="2">
                        <ui:SymbolIcon Symbol="{Binding Icon}" Margin="0,0,5,0" Width="16" Height="16"/>
                        <TextBlock Text="{Binding Name}" FontWeight="SemiBold"/>
                        <TextBlock Text="{Binding Description}" Margin="5,0,0,0" 
                                   Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                                   FontStyle="Italic"/>
                    </StackPanel>
                </HierarchicalDataTemplate>
            </TreeView.ItemTemplate>
        </TreeView>
    </Grid>
</UserControl>

<!-- Controls/PropertiesPanelControl.xaml -->
<UserControl x:Class="DNP3Editor.Controls.PropertiesPanelControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="5">
            <TextBlock Text="{Binding SelectedObjectName}" FontWeight="Bold" FontSize="14"/>
            <TextBlock Text="{Binding SelectedObjectType}" 
                       Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
        </StackPanel>

        <!-- Properties Grid -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding Properties}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Grid Margin="5,2">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="{Binding Name}" 
                                       VerticalAlignment="Center" FontWeight="SemiBold"/>
                            <TextBox Grid.Column="1" Text="{Binding Value}" 
                                     IsReadOnly="{Binding IsReadOnly}"
                                     VerticalAlignment="Center"/>
                        </Grid>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>

<!-- Controls/OutputWindowControl.xaml -->
<UserControl x:Class="DNP3Editor.Controls.OutputWindowControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
            <CheckBox Content="Info" IsChecked="{Binding ShowInfo}" Margin="0,0,10,0"/>
            <CheckBox Content="Warnings" IsChecked="{Binding ShowWarnings}" Margin="0,0,10,0"/>
            <CheckBox Content="Errors" IsChecked="{Binding ShowErrors}" Margin="0,0,10,0"/>
            <Button Content="Clear" Command="{Binding ClearCommand}" Margin="10,0,0,0"/>
        </StackPanel>

        <!-- Messages List -->
        <ListBox Grid.Row="1" ItemsSource="{Binding Messages}" 
                 ScrollViewer.HorizontalScrollBarVisibility="Auto">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <Grid Margin="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <ui:SymbolIcon Grid.Column="0" Margin="0,0,5,0" Width="16" Height="16">
                            <ui:SymbolIcon.Style>
                                <Style TargetType="ui:SymbolIcon">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Type}" Value="Info">
                                            <Setter Property="Symbol" Value="Info"/>
                                            <Setter Property="Foreground" Value="Blue"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Type}" Value="Warning">
                                            <Setter Property="Symbol" Value="Important"/>
                                            <Setter Property="Foreground" Value="Orange"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Type}" Value="Error">
                                            <Setter Property="Symbol" Value="Cancel"/>
                                            <Setter Property="Foreground" Value="Red"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Type}" Value="Success">
                                            <Setter Property="Symbol" Value="Accept"/>
                                            <Setter Property="Foreground" Value="Green"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ui:SymbolIcon.Style>
                        </ui:SymbolIcon>

                        <TextBlock Grid.Column="1" Text="{Binding Timestamp, StringFormat=HH:mm:ss}" 
                                   Margin="0,0,10,0" VerticalAlignment="Center"
                                   Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
                        
                        <TextBlock Grid.Column="2" Text="{Binding Message}" 
                                   TextWrapping="Wrap" VerticalAlignment="Center"/>
                    </Grid>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
    </Grid>
</UserControl>

<!-- Views/DeviceConfigurationView.xaml -->
<UserControl x:Class="DNP3Editor.Views.DeviceConfigurationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="10">
            <!-- Device Information Section -->
            <GroupBox Header="Device Information" Margin="0,0,0,10">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Label Grid.Row="0" Grid.Column="0" Content="Vendor Name:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding VendorName}" Margin="5"/>

                    <Label Grid.Row="1" Grid.Column="0" Content="Device Name:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding DeviceName}" Margin="5"/>

                    <Label Grid.Row="2" Grid.Column="0" Content="Hardware Version:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding HardwareVersion}" Margin="5"/>

                    <Label Grid.Row="3" Grid.Column="0" Content="Software Version:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding SoftwareVersion}" Margin="5"/>
                </Grid>
            </GroupBox>

            <!-- Device Function Section -->
            <GroupBox Header="Device Function" Margin="0,0,0,10">
                <StackPanel Margin="10">
                    <RadioButton Content="Master Device" IsChecked="{Binding IsMasterDevice}" Margin="0,5"/>
                    <RadioButton Content="Outstation Device" IsChecked="{Binding IsOutstationDevice}" Margin="0,5"/>
                </StackPanel>
            </GroupBox>

            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10">
                <Button Content="Save" Command="{Binding SaveConfigurationCommand}" 
                        Style="{StaticResource AccentButtonStyle}" Margin="0,0,10,0"/>
                <Button Content="Reset" Command="{Binding ResetConfigurationCommand}"/>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</UserControl>

<!-- Views/DataPointsView.xaml -->
<UserControl x:Class="DNP3Editor.Views.DataPointsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <ToolBarTray Grid.Row="0">
            <ToolBar>
                <ComboBox SelectedItem="{Binding SelectedDataPointType}" Width="150">
                    <ComboBoxItem Content="Binary Inputs"/>
                    <ComboBoxItem Content="Analog Inputs"/>
                    <ComboBoxItem Content="Binary Outputs"/>
                    <ComboBoxItem Content="Analog Outputs"/>
                    <ComboBoxItem Content="Counters"/>
                </ComboBox>
                <Separator/>
                <Button Command="{Binding AddDataPointCommand}" ToolTip="Add Data Point">
                    <ui:SymbolIcon Symbol="Add"/>
                </Button>
                <Button Command="{Binding DeleteDataPointCommand}" ToolTip="Delete Data Point">
                    <ui:SymbolIcon Symbol="Delete"/>
                </Button>
                <Button Command="{Binding DuplicateDataPointCommand}" ToolTip="Duplicate Data Point">
                    <ui:SymbolIcon Symbol="Copy"/>
                </Button>
                <Separator/>
                <Button Command="{Binding ImportDataPointsCommand}" ToolTip="Import from CSV">
                    <ui:SymbolIcon Symbol="Download"/>
                </Button>
                <Button Command="{Binding ExportDataPointsCommand}" ToolTip="Export to CSV">
                    <ui:SymbolIcon Symbol="Upload"/>
                </Button>
            </ToolBar>
        </ToolBarTray>

        <!-- Data Points Grid -->
        <TabControl Grid.Row="1">
            <TabItem Header="Binary Inputs">
                <DataGrid ItemsSource="{Binding BinaryInputPoints}" 
                          SelectedItem="{Binding SelectedDataPoint}"
                          AutoGenerateColumns="False" CanUserAddRows="False">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Index" Binding="{Binding Index}" Width="60"/>
                        <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="200"/>
                        <DataGridComboBoxColumn Header="Event Class" 
                                                SelectedItemBinding="{Binding ChangeEventClass}" Width="100">
                            <DataGridComboBoxColumn.ItemsSource>
                                <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                    <sys:String>None</sys:String>
                                    <sys:String>One</sys:String>
                                    <sys:String>Two</sys:String>
                                    <sys:String>Three</sys:String>
                                </x:Array>
                            </DataGridComboBoxColumn.ItemsSource>
                        </DataGridComboBoxColumn>
                        <DataGridTextColumn Header="State 0 Name" Binding="{Binding NameState0}" Width="100"/>
                        <DataGridTextColumn Header="State 1 Name" Binding="{Binding NameState1}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>

            <TabItem Header="Analog Inputs">
                <DataGrid ItemsSource="{Binding AnalogInputPoints}" 
                          SelectedItem="{Binding SelectedDataPoint}"
                          AutoGenerateColumns="False" CanUserAddRows="False">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Index" Binding="{Binding Index}" Width="60"/>
                        <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="200"/>
                        <DataGridTextColumn Header="Units" Binding="{Binding Units}" Width="80"/>
                        <DataGridTextColumn Header="Scale Factor" Binding="{Binding ScaleFactor}" Width="100"/>
                        <DataGridTextColumn Header="Scale Offset" Binding="{Binding ScaleOffset}" Width="100"/>
                        <DataGridComboBoxColumn Header="Event Class" 
                                                SelectedItemBinding="{Binding ChangeEventClass}" Width="100">
                            <DataGridComboBoxColumn.ItemsSource>
                                <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                    <sys:String>None</sys:String>
                                    <sys:String>One</sys:String>
                                    <sys:String>Two</sys:String>
                                    <sys:String>Three</sys:String>
                                </x:Array>
                            </DataGridComboBoxColumn.ItemsSource>
                        </DataGridComboBoxColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>

            <!-- Add other tabs for different point types... -->
        </TabControl>
    </Grid>
</UserControl>