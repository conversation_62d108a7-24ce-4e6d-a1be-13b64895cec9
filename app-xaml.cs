using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using System.Windows;
using DNP3Editor.Services;
using DNP3Editor.ViewModels;
using DNP3Editor.Views;

namespace DNP3Editor;

public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.File("logs/dnp3editor-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        // Build DI container
        _host = Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                ConfigureServices(services);
            })
            .Build();

        await _host.StartAsync();

        // Show main window
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    private static void ConfigureServices(IServiceCollection services)
    {
        // Services
        services.AddSingleton<IXmlDocumentService, XmlDocumentService>();
        services.AddSingleton<ILicenseService, LicenseService>();
        services.AddSingleton<IValidationService, ValidationService>();
        services.AddSingleton<IFileDialogService, FileDialogService>();
        services.AddSingleton<IMessageBoxService, MessageBoxService>();

        // ViewModels
        services.AddTransient<MainWindowViewModel>();
        services.AddTransient<DeviceConfigurationViewModel>();
        services.AddTransient<DataPointsViewModel>();
        services.AddTransient<TreeNavigationViewModel>();

        // Views
        services.AddTransient<MainWindow>();
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }

        Log.CloseAndFlush();
        base.OnExit(e);
    }
}