// ViewModels/MainWindowViewModel.cs
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;
using DNP3Editor.Models;
using DNP3Editor.Services;
using DNP3Editor.Generated;


namespace DNP3Editor.ViewModels;

public partial class MainWindowViewModel : ObservableObject
{
    private readonly IXmlDocumentService _documentService;
    private readonly ILicenseService _licenseService;
    private readonly IFileDialogService _fileDialogService;
    private readonly IMessageBoxService _messageBoxService;
    private readonly ILogger<MainWindowViewModel> _logger;

    [ObservableProperty]
    private string windowTitle = "DNP3 Device Profile Editor";

    [ObservableProperty]
    private string statusMessage = "Ready";

    [ObservableProperty]
    private DNP3DeviceProfileDocument? currentDocument;

    [ObservableProperty]
    private string? currentFilePath;

    [ObservableProperty]
    private bool isDocumentModified;

    [ObservableProperty]
    private TabItemViewModel? selectedTab;

    [ObservableProperty]
    private bool isNavigationTreeVisible = true;

    [ObservableProperty]
    private bool isPropertiesPanelVisible = true;

    [ObservableProperty]
    private bool isOutputWindowVisible = true;

    public ObservableCollection<TabItemViewModel> OpenTabs { get; } = new();
    public ObservableCollection<RecentFileViewModel> RecentFiles { get; } = new();

    // Child ViewModels - injected or created as needed
    public TreeNavigationViewModel NavigationViewModel { get; }
    public PropertiesPanelViewModel PropertiesViewModel { get; }
    public OutputWindowViewModel OutputViewModel { get; }
    public ValidationViewModel ValidationViewModel { get; }

    public MainWindowViewModel(
        IXmlDocumentService documentService,
        ILicenseService licenseService,
        IFileDialogService fileDialogService,
        IMessageBoxService messageBoxService,
        TreeNavigationViewModel navigationViewModel,
        PropertiesPanelViewModel propertiesViewModel,
        OutputWindowViewModel outputViewModel,
        ValidationViewModel validationViewModel,
        ILogger<MainWindowViewModel> logger)
    {
        _documentService = documentService;
        _licenseService = licenseService;
        _fileDialogService = fileDialogService;
        _messageBoxService = messageBoxService;
        _logger = logger;

        NavigationViewModel = navigationViewModel;
        PropertiesViewModel = propertiesViewModel;
        OutputViewModel = outputViewModel;
        ValidationViewModel = validationViewModel;

        // Wire up events
        NavigationViewModel.NodeSelected += OnNavigationNodeSelected;
        ValidationViewModel.ValidationCompleted += OnValidationCompleted;
        
        // Initialize
        LoadRecentFiles();
        CheckLicense();
        UpdateWindowTitle();
    }

    #region Document Commands

    [RelayCommand]
    private async Task NewDocument()
    {
        if (!await ConfirmUnsavedChanges()) return;

        try
        {
            var documentFactory = new DocumentFactory();
            CurrentDocument = documentFactory.CreateNewDocument();

            CurrentFilePath = null;
            IsDocumentModified = false;
            StatusMessage = "New document created";
            
            await LoadDocumentIntoViewModels();
            
            _logger.LogInformation("New document created");
        }
        catch (Exception ex)
        {
            await HandleError("Error creating new document", ex);
        }
    }

    [RelayCommand]
    private async Task OpenDocument()
    {
        if (!await ConfirmUnsavedChanges()) return;

        var filePath = await _fileDialogService.ShowOpenFileDialogAsync(
            "Open DNP3 Device Profile",
            "XML Files (*.xml)|*.xml|All Files (*.*)|*.*");

        if (!string.IsNullOrEmpty(filePath))
        {
            await LoadDocument(filePath);
        }
    }

    [RelayCommand]
    private async Task SaveDocument()
    {
        if (CurrentDocument == null) return;

        if (string.IsNullOrEmpty(CurrentFilePath))
        {
            await SaveAsDocument();
        }
        else
        {
            await SaveDocumentToFile(CurrentFilePath);
        }
    }

    [RelayCommand]
    private async Task SaveAsDocument()
    {
        if (CurrentDocument == null) return;

        var filePath = await _fileDialogService.ShowSaveFileDialogAsync(
            "Save DNP3 Device Profile",
            "XML Files (*.xml)|*.xml",
            "DeviceProfile.xml");

        if (!string.IsNullOrEmpty(filePath))
        {
            await SaveDocumentToFile(filePath);
        }
    }

    #endregion

    #region View Commands

    [RelayCommand]
    private async Task ValidateDocument()
    {
        if (CurrentDocument == null) return;
        await ValidationViewModel.ValidateDocumentAsync(CurrentDocument);
    }

    [RelayCommand]
    private async Task ShowDataPoints()
    {
        if (CurrentDocument?.ReferenceDevice?.DataPointsList == null)
        {
            await _messageBoxService.ShowWarningAsync("No Data Points", 
                "The current document does not contain data points.");
            return;
        }

        var tabManager = new TabManager(OpenTabs);
        var tab = await tabManager.GetOrCreateDataPointsTab(CurrentDocument.ReferenceDevice.DataPointsList);
        SelectedTab = tab;
    }

    [RelayCommand]
    private void CloseTab(TabItemViewModel? tab)
    {
        if (tab != null)
        {
            var tabManager = new TabManager(OpenTabs);
            tabManager.CloseTab(tab);
            
            if (SelectedTab == tab && OpenTabs.Count > 0)
            {
                SelectedTab = OpenTabs[0];
            }
        }
    }

    [RelayCommand]
    private async Task OpenRecent(string filePath)
    {
        if (!await ConfirmUnsavedChanges()) return;
        await LoadDocument(filePath);
    }

    #endregion

    #region Application Commands

    [RelayCommand]
    private void ShowPreferences()
    {
        // TODO: Show preferences dialog
        var preferencesViewModel = new PreferencesViewModel();
        // Show dialog...
    }

    [RelayCommand]
    private void ShowDocumentation()
    {
        // TODO: Open documentation
        DocumentationHelper.OpenUserGuide();
    }

    [RelayCommand]
    private void ShowAbout()
    {
        var aboutViewModel = new AboutViewModel(_licenseService);
        // Show about dialog...
    }

    [RelayCommand]
    private async Task ExitApplication()
    {
        if (await ConfirmUnsavedChanges())
        {
            Application.Current.Shutdown();
        }
    }

    #endregion

    #region Private Methods

    private async Task LoadDocument(string filePath)
    {
        try
        {
            StatusMessage = "Loading document...";
            
            CurrentDocument = await _documentService.LoadDocumentAsync(filePath);
            CurrentFilePath = filePath;
            IsDocumentModified = false;
            
            await LoadDocumentIntoViewModels();
            AddToRecentFiles(filePath);
            
            StatusMessage = "Document loaded successfully";
            _logger.LogInformation("Document loaded from {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            await HandleError($"Error loading document from {filePath}", ex);
        }
    }

    private async Task SaveDocumentToFile(string filePath)
    {
        if (CurrentDocument == null) return;

        try
        {
            StatusMessage = "Saving document...";
            
            await _documentService.SaveDocumentAsync(CurrentDocument, filePath);
            
            CurrentFilePath = filePath;
            IsDocumentModified = false;
            StatusMessage = "Document saved successfully";
            
            AddToRecentFiles(filePath);
            _logger.LogInformation("Document saved to {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            await HandleError($"Error saving document to {filePath}", ex);
        }
    }

    private async Task LoadDocumentIntoViewModels()
    {
        if (CurrentDocument == null) return;

        NavigationViewModel.LoadDocument(CurrentDocument);
        
        var tabManager = new TabManager(OpenTabs);
        await tabManager.LoadDefaultTabs(CurrentDocument);
        
        if (OpenTabs.Count > 0)
        {
            SelectedTab = OpenTabs[0];
        }
    }

    private async Task<bool> ConfirmUnsavedChanges()
    {
        if (!IsDocumentModified) return true;

        var result = await _messageBoxService.ShowQuestionAsync(
            "Unsaved Changes",
            "The current document has unsaved changes. Do you want to save them?",
            MessageBoxButton.YesNoCancel);

        return result switch
        {
            MessageBoxResult.Yes => await TrySaveCurrentDocument(),
            MessageBoxResult.No => true,
            _ => false
        };
    }

    private async Task<bool> TrySaveCurrentDocument()
    {
        try
        {
            if (string.IsNullOrEmpty(CurrentFilePath))
            {
                await SaveAsDocument();
            }
            else
            {
                await SaveDocument();
            }
            return !IsDocumentModified;
        }
        catch
        {
            return false;
        }
    }

    private async Task HandleError(string message, Exception ex)
    {
        _logger.LogError(ex, message);
        await _messageBoxService.ShowErrorAsync("Error", $"{message}: {ex.Message}");
        StatusMessage = message;
    }

    private void OnNavigationNodeSelected(object? sender, NavigationNodeSelectedEventArgs e)
    {
        PropertiesViewModel.LoadProperties(e.SelectedNode);
    }

    private void OnValidationCompleted(object? sender, ValidationCompletedEventArgs e)
    {
        // Update UI based on validation results
        OutputViewModel.Clear();
        OutputViewModel.AddMessage("Validation Results:", MessageType.Info);
        
        if (e.Result.IsValid)
        {
            OutputViewModel.AddMessage("Document is valid!", MessageType.Success);
        }
        else
        {
            foreach (var error in e.Result.Errors)
            {
                OutputViewModel.AddMessage($"Error: {error}", MessageType.Error);
            }
        }
    }

    private void UpdateWindowTitle()
    {
        var title = "DNP3 Device Profile Editor";
        
        if (!string.IsNullOrEmpty(CurrentFilePath))
        {
            title = $"{Path.GetFileName(CurrentFilePath)} - {title}";
        }
        else if (CurrentDocument != null)
        {
            title = $"New Document - {title}";
        }

        if (IsDocumentModified)
        {
            title = $"*{title}";
        }

        WindowTitle = title;
    }

    private void LoadRecentFiles()
    {
        // TODO: Load from settings
        var recentFilesService = new RecentFilesService();
        var recentFiles = recentFilesService.GetRecentFiles();
        
        RecentFiles.Clear();
        foreach (var file in recentFiles)
        {
            RecentFiles.Add(file);
        }
    }

    private void AddToRecentFiles(string filePath)
    {
        var recentFilesService = new RecentFilesService();
        recentFilesService.AddRecentFile(filePath);
        LoadRecentFiles();
    }

    private void CheckLicense()
    {
        if (!_licenseService.IsLicenseValid())
        {
            // TODO: Show license dialog
            var licenseViewModel = new LicenseViewModel(_licenseService);
            // Show license dialog...
        }
    }

    #endregion

    #region Property Change Handlers

    partial void OnCurrentDocumentChanged(DNP3DeviceProfileDocument? value)
    {
        UpdateWindowTitle();
    }

    partial void OnCurrentFilePathChanged(string? value)
    {
        UpdateWindowTitle();
    }

    partial void OnIsDocumentModifiedChanged(bool value)
    {
        UpdateWindowTitle();
    }

    #endregion
}

// ViewModels/ValidationViewModel.cs
using CommunityToolkit.Mvvm.ComponentModel;
using DNP3Editor.Services;
using DNP3Editor.Models;
using DNP3Editor.Events;
using DNP3Editor.Generated;

namespace DNP3Editor.ViewModels;

public partial class ValidationViewModel : ObservableObject
{
    private readonly IValidationService _validationService;

    [ObservableProperty]
    private string validationStatus = "Not Validated";

    [ObservableProperty]
    private bool isValidating;

    public event EventHandler<ValidationCompletedEventArgs>? ValidationCompleted;

    public ValidationViewModel(IValidationService validationService)
    {
        _validationService = validationService;
    }

    public async Task ValidateDocumentAsync(DNP3DeviceProfileDocument document)
    {
        IsValidating = true;
        ValidationStatus = "Validating...";

        try
        {
            var result = await _validationService.ValidateDocumentAsync(document);
            
            ValidationStatus = result.IsValid 
                ? "Valid" 
                : $"Errors: {result.Errors.Count}";

            ValidationCompleted?.Invoke(this, new ValidationCompletedEventArgs(result));
        }
        catch (Exception ex)
        {
            ValidationStatus = "Validation Failed";
            var errorResult = new ValidationResult
            {
                IsValid = false,
                Errors = new List<string> { ex.Message }
            };
            ValidationCompleted?.Invoke(this, new ValidationCompletedEventArgs(errorResult));
        }
        finally
        {
            IsValidating = false;
        }
    }
}

// Services/DocumentFactory.cs
using DNP3Editor.Generated;

namespace DNP3Editor.Services;

public class DocumentFactory
{
    public DNP3DeviceProfileDocument CreateNewDocument()
    {
        return new DNP3DeviceProfileDocument
        {
            SchemaVersion = "2.10.00",
            DocumentHeader = CreateDefaultHeader(),
            ReferenceDevice = CreateDefaultDevice()
        };
    }

    private DocumentHeaderType CreateDefaultHeader()
    {
        return new DocumentHeaderType
        {
            DocumentName = "New DNP3 Device Profile",
            DocumentDescription = "Device profile created with DNP3 Editor",
            RevisionHistory = new[]
            {
                new DocumentHeaderTypeRevisionHistory
                {
                    Version = 1,
                    Date = DateTime.Today,
                    Author = Environment.UserName,
                    Reason = "Initial creation"
                }
            }
        };
    }

    private DNP3DeviceOptionalType CreateDefaultDevice()
    {
        return new DNP3DeviceOptionalType
        {
            Description = "Reference Device",
            Configuration = new DNP3ConfigurationType
            {
                DeviceConfig = new DeviceConfigType
                {
                    DeviceName = new DeviceNameType
                    {
                        CurrentValue = new DeviceNameCurrentValueType
                        {
                            Value = "New Device"
                        }
                    },
                    DeviceFunction = new DeviceFunctionType
                    {
                        CurrentValue = new DeviceFunctionCurrentValueType
                        {
                            Outstation = new EmptyElement()
                        }
                    }
                }
            }
        };
    }
}

// Services/TabManager.cs
using System.Collections.ObjectModel;
using DNP3Editor.Models;
using DNP3Editor.ViewModels;
using DNP3Editor.Views;
using DNP3Editor.Generated;

namespace DNP3Editor.Services;

public class TabManager
{
    private readonly ObservableCollection<TabItemViewModel> _tabs;

    public TabManager(ObservableCollection<TabItemViewModel> tabs)
    {
        _tabs = tabs;
    }

    public async Task LoadDefaultTabs(DNP3DeviceProfileDocument document)
    {
        _tabs.Clear();

        if (document.ReferenceDevice?.Configuration != null)
        {
            var configTab = CreateDeviceConfigurationTab(document.ReferenceDevice.Configuration);
            _tabs.Add(configTab);
        }
    }

    public async Task<TabItemViewModel> GetOrCreateDataPointsTab(DNP3DataPointsListType dataPointsList)
    {
        var existingTab = _tabs.FirstOrDefault(t => t.Id == "datapoints");
        if (existingTab != null)
        {
            return existingTab;
        }

        var viewModel = new DataPointsViewModel(dataPointsList);
        var tab = new TabItemViewModel
        {
            Id = "datapoints",
            Header = "Data Points",
            Content = new DataPointsView { DataContext = viewModel }
        };

        _tabs.Add(tab);
        return tab;
    }

    public void CloseTab(TabItemViewModel tab)
    {
        if (tab.CanClose && _tabs.Contains(tab))
        {
            _tabs.Remove(tab);
        }
    }

    private TabItemViewModel CreateDeviceConfigurationTab(DNP3ConfigurationType configuration)
    {
        var viewModel = new DeviceConfigurationViewModel(configuration);
        return new TabItemViewModel
        {
            Id = "configuration",
            Header = "Device Configuration",
            Content = new DeviceConfigurationView { DataContext = viewModel }
        };
    }
}

// Services/RecentFilesService.cs
using DNP3Editor.Models;

namespace DNP3Editor.Services;

public class RecentFilesService
{
    private const int MaxRecentFiles = 10;
    private readonly string _settingsPath;

    public RecentFilesService()
    {
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        var appFolder = Path.Combine(appDataPath, "DNP3Editor");
        Directory.CreateDirectory(appFolder);
        _settingsPath = Path.Combine(appFolder, "recent-files.json");
    }

    public List<RecentFileViewModel> GetRecentFiles()
    {
        try
        {
            if (!File.Exists(_settingsPath))
                return new List<RecentFileViewModel>();

            var json = File.ReadAllText(_settingsPath);
            var files = System.Text.Json.JsonSerializer.Deserialize<List<RecentFileData>>(json);
            
            return files?
                .Where(f => File.Exists(f.FilePath))
                .OrderByDescending(f => f.LastAccessed)
                .Select(f => new RecentFileViewModel 
                { 
                    FilePath = f.FilePath, 
                    LastAccessed = f.LastAccessed 
                })
                .ToList() ?? new List<RecentFileViewModel>();
        }
        catch
        {
            return new List<RecentFileViewModel>();
        }
    }

    public void AddRecentFile(string filePath)
    {
        var recentFiles = GetRecentFiles();
        
        // Remove if already exists
        recentFiles.RemoveAll(f => f.FilePath.Equals(filePath, StringComparison.OrdinalIgnoreCase));
        
        // Add to top
        recentFiles.Insert(0, new RecentFileViewModel 
        { 
            FilePath = filePath, 
            LastAccessed = DateTime.Now 
        });
        
        // Limit to max files
        if (recentFiles.Count > MaxRecentFiles)
        {
            recentFiles = recentFiles.Take(MaxRecentFiles).ToList();
        }

        SaveRecentFiles(recentFiles);
    }

    private void SaveRecentFiles(List<RecentFileViewModel> recentFiles)
    {
        try
        {
            var data = recentFiles.Select(f => new RecentFileData 
            { 
                FilePath = f.FilePath, 
                LastAccessed = f.LastAccessed 
            }).ToList();
            
            var json = System.Text.Json.JsonSerializer.Serialize(data, new System.Text.Json.JsonSerializerOptions 
            { 
                WriteIndented = true 
            });
            
            File.WriteAllText(_settingsPath, json);
        }
        catch
        {
            // Ignore save errors
        }
    }

    private class RecentFileData
    {
        public string FilePath { get; set; } = string.Empty;
        public DateTime LastAccessed { get; set; }
    }
}

// Utilities/DocumentationHelper.cs
using System.Diagnostics;

namespace DNP3Editor.Utilities;

public static class DocumentationHelper
{
    public static void OpenUserGuide()
    {
        var userGuidePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documentation", "UserGuide.pdf");
        
        if (File.Exists(userGuidePath))
        {
            Process.Start(new ProcessStartInfo(userGuidePath) { UseShellExecute = true });
        }
        else
        {
            // Open online documentation
            Process.Start(new ProcessStartInfo("https://your-company.com/dnp3editor/docs") { UseShellExecute = true });
        }
    }
}

// Events/ValidationCompletedEventArgs.cs
using DNP3Editor.Models;

namespace DNP3Editor.Events;

public class ValidationCompletedEventArgs : EventArgs
{
    public ValidationResult Result { get; }

    public ValidationCompletedEventArgs(ValidationResult result)
    {
        Result = result;
    }
}